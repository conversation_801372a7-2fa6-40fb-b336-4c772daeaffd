## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 6: Add Game to Merchant Entity
POST {{MAPI_URL}}/v1/entities/seamless_test_merchant/games/sw_mrmnky
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "status": "normal",
  "settings": {}
}
HTTP 201
