## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 3: Create Merchant Entity in MAPI under master entity
POST {{MAPI_URL}}/v1/merchantentities
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "name": "SeamlessTestMerchant",
  "code": "seamless_test_merchant",
  "type": "seamless",
  "defaultCountry": "US",
  "defaultCurrency": "USD",
  "defaultLanguage": "en",
  "jurisdictionCode": "COM",
  "webSiteUrl": "http://localhost:3000",
  "params": {
    "serverUrl": "{{MOCK_URL}}",
    "password": "SecurePassword123!"
  }
}
HTTP 201
