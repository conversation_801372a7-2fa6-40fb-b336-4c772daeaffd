## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 7: Setup IMP-Mock Merchant
POST {{MOCK_URL}}/v1/merchant
accept: application/json
Content-Type: application/json
{
  "merch_id": "seamless_test_merchant",
  "merch_pwd": "SecurePassword123!",
  "isPromoInternal": false,
  "multiple_session": true,
  "serverUrl": "{{MOCK_URL}}",
  "type": "seamless"
}
HTTP *
[Asserts]
status >= 200
status < 500

## Step 8: Create Test Customer in IMP-Mock
POST {{MOCK_URL}}/v1/merchant/seamless_test_merchant/customer
accept: application/json
Content-Type: application/json
{
  "cust_id": "test_player_001",
  "cust_login": "TESTPLAYER001",
  "currency_code": "USD",
  "language": "en",
  "country": "US",
  "test_cust": false,
  "status": "normal",
  "bet_limit": 1000,
  "first_name": "Test",
  "last_name": "Player",
  "email": "<EMAIL>",
  "jurisdiction": "COM",
  "balance": {
    "amount": 100000,
    "currency_code": "USD"
  }
}
HTTP 201

## Step 9: Generate Customer Ticket
GET {{MOCK_URL}}/v1/merchant/seamless_test_merchant/customer/test_player_001/ticket
accept: text/plain
HTTP 200
[Captures]
playerTicket: body

## Step 10: Get Game URL for Seamless Integration
POST {{MAPI_URL}}/v1/merchants/game/url
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "merchantType": "seamless",
  "merchantCode": "seamless_test_merchant",
  "gameCode": "{{GAME_CODE}}",
  "playmode": "real",
  "language": "en",
  "ip": "127.0.0.1",
  "lobby": "http://localhost:3000/lobby",
  "cashier": "http://localhost:3000/cashier",
  "ticket": "{{playerTicket}}",
  "currency": "USD"
}
HTTP 200
[Captures]
gameUrl: jsonpath "$.url"
gameToken: jsonpath "$.token"
[Asserts]
jsonpath "$.url" exists
jsonpath "$.token" exists
jsonpath "$.currency" == "USD"
