## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 4: Create Game Provider
POST {{MAPI_URL}}/v1/gameproviders
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "user": "skywind_test",
  "code": "sw_test",
  "title": "Skywind Test Provider",
  "secret": "test_secret_123",
  "isTest": true
}
HTTP *
[Asserts]
status >= 200
status < 500
[Captures]
providerId: jsonpath "$.id"

## Step 5: Create Game sw_mrmnky
POST {{MAPI_URL}}/v1/game/slot
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "type": "slot",
  "title": "Monkey Slot",
  "url": "http://gameserver.test.com/games/sw_mrmnky?token={startGameToken}",
  "gameCode": "sw_mrmnky",
  "providerGameCode": "sw_mrmnky",
  "defaultInfo": {
    "name": "Monkey Slot",
    "description": "A fun monkey-themed slot game"
  },
  "info": {
    "EN": {
      "name": "Monkey Slot",
      "description": "A fun monkey-themed slot game"
    }
  },
  "limits": {
    "USD": {
      "coinsRate": 0.01
    }
  },
  "providerId": "{{providerId}}"
}
HTTP 201
