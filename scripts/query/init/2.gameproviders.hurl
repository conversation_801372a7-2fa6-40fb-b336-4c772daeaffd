## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 4: Create Game Provider
POST {{MAPI_URL}}/v1/gameproviders
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "user": "skywind_test",
  "code": "sw_test",
  "title": "Skywind Test Provider",
  "secret": "test_secret_123",
  "isTest": true
}
HTTP 201
