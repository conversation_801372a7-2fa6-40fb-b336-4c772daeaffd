## Step 1: Management API Authentication
POST {{MAPI_URL}}/v1/login
accept: application/json
Content-Type: application/json
{
    "secretKey": "aaa11200-19f1-48c1-a78c-3a3d56095f38",
    "username": "SUPERADMIN",
    "password": "{{MAPI_PASSWORD}}"
}
HTTP 200
[Captures]
accessToken: jsonpath "$['accessToken']"
[Asserts]
jsonpath "$.accessToken" exists

## Step 2: Create Seamless Merchant Type
POST {{MAPI_URL}}/v1/merchant-types
accept: application/json
Content-Type: application/json
x-access-token: {{accessToken}}
{
  "type": "seamless",
  "url": "{{WALLET_URL}}",
  "schema": {
    "serverUrl": {
      "type": "text",
      "title": "Server URL",
      "defaultValue": "http://localhost:8000"
    },
    "password": {
      "type": "password",
      "title": "Password",
      "defaultValue": ""
    }
  }
}
HTTP 200
